//
//  LotteryConfigTestView.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/31.
//

import SwiftUI

/**
 * 抽奖配置测试页面
 * 用于测试成员选择和抽奖道具选择弹窗
 */
struct LotteryConfigTestView: View {
    
    @EnvironmentObject private var dataManager: DataManager
    @State private var showMemberSelectionPopup = false
    @State private var showLotteryToolSelectionPopup = false
    @State private var selectedMemberForLottery: Member?
    @State private var selectedToolType: LotteryConfig.ToolType?
    @State private var resultMessage = ""
    
    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                
                // 标题
                Text("抽奖配置测试")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                // 当前状态显示
                VStack(spacing: 16) {
                    if let member = selectedMemberForLottery {
                        HStack {
                            Text("已选择成员:")
                                .font(.headline)
                            Text(member.name ?? "未知成员")
                                .font(.headline)
                                .foregroundColor(Color(hex: "#a9d051"))
                        }
                    }
                    
                    if let toolType = selectedToolType {
                        HStack {
                            Text("已选择道具:")
                                .font(.headline)
                            Text(toolType.displayName)
                                .font(.headline)
                                .foregroundColor(Color(hex: "#a9d051"))
                        }
                    }
                    
                    if !resultMessage.isEmpty {
                        Text(resultMessage)
                            .font(.body)
                            .foregroundColor(.blue)
                            .multilineTextAlignment(.center)
                            .padding()
                            .background(Color.blue.opacity(0.1))
                            .cornerRadius(8)
                    }
                }
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(12)
                
                // 测试按钮
                VStack(spacing: 16) {
                    Button("开始配置抽奖道具") {
                        selectedMemberForLottery = nil
                        selectedToolType = nil
                        resultMessage = ""
                        showMemberSelectionPopup = true
                    }
                    .font(.headline)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color(hex: "#a9d051"))
                    .cornerRadius(12)
                    
                    Button("重置") {
                        selectedMemberForLottery = nil
                        selectedToolType = nil
                        resultMessage = ""
                    }
                    .font(.headline)
                    .foregroundColor(Color(hex: "#a9d051"))
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color(hex: "#a9d051").opacity(0.1))
                    .cornerRadius(12)
                }
                
                // 成员列表状态
                VStack(alignment: .leading, spacing: 8) {
                    Text("当前成员列表:")
                        .font(.headline)
                    
                    if dataManager.members.isEmpty {
                        Text("暂无成员，请先添加成员")
                            .foregroundColor(.gray)
                    } else {
                        ForEach(dataManager.members, id: \.id) { member in
                            HStack {
                                Text("• \(member.name ?? "未知成员")")
                                Spacer()
                                Text("\(member.currentPoints)积分")
                                    .foregroundColor(Color(hex: "#a9d051"))
                            }
                        }
                    }
                }
                .padding()
                .background(Color.gray.opacity(0.05))
                .cornerRadius(8)
                
                Spacer()
            }
            .padding()
            .navigationTitle("抽奖配置测试")
            .navigationBarTitleDisplayMode(.inline)
        }
        // 成员选择弹窗
        .overlay(
            MemberSelectionPopupView(
                isPresented: $showMemberSelectionPopup,
                members: dataManager.members,
                onMemberSelected: { member in
                    selectedMemberForLottery = member
                    showMemberSelectionPopup = false
                    showLotteryToolSelectionPopup = true
                    resultMessage = "已选择成员: \(member.name ?? "未知成员")"
                }
            )
        )
        // 抽奖道具选择弹窗
        .overlay(
            LotteryToolSelectionPopupView(
                isPresented: $showLotteryToolSelectionPopup,
                selectedMember: selectedMemberForLottery,
                onToolSelected: { member, toolType in
                    selectedToolType = toolType
                    showLotteryToolSelectionPopup = false
                    resultMessage = "为成员 \(member.name ?? "未知成员") 选择了 \(toolType.displayName)"
                }
            )
        )
    }
}

#Preview {
    LotteryConfigTestView()
        .environmentObject(DataManager.shared)
}
